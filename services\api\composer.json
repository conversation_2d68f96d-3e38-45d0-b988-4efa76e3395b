{"type": "project", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.4", "ext-ffi": "*", "ext-gd": "*", "ext-imagick": "*", "ext-vips": "*", "aws/aws-sdk-php": "^3.336", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.16", "eventsauce/eventsauce": "^3.6", "eventsauce/message-repository-for-doctrine": "^1.1", "icewind/smb": "^3.6", "jcupitt/vips": "^2.5", "lexik/jwt-authentication-bundle": "3.1.0", "nelmio/cors-bundle": "^2.5", "predis/predis": "^2.3", "ramsey/uuid": "^4.7", "ramsey/uuid-doctrine": "^2.0", "runtime/swoole": "^0.4.0", "symfony/cache": "^7.3", "symfony/console": "^7.3", "symfony/doctrine-messenger": "^7.3", "symfony/dotenv": "^7.3", "symfony/flex": "^2", "symfony/framework-bundle": "^7.3", "symfony/messenger": "^7.3", "symfony/mime": "^7.3", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "^7.3", "symfony/property-info": "^7.3", "symfony/runtime": "^7.3", "symfony/serializer": "^7.3", "symfony/validator": "^7.3", "symfony/yaml": "^7.3"}, "require-dev": {"dg/bypass-finals": "dev-master", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^12.2.5", "symfony/phpunit-bridge": "^7.3", "symfony/stopwatch": "^7.3", "symfony/web-profiler-bundle": "^7.3"}, "config": {"platform": {"php": "8.4", "ext-ffi": "8.4.0", "ext-imagick": "3.7.0", "ext-vips": "1.0.13"}, "allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"Slink\\": "src/Slink", "UI\\": "src/UI"}}, "autoload-dev": {"psr-4": {"Unit\\Slink\\": "tests/Unit/Slink", "Tests\\": "tests"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "phpstan": "vendor/bin/phpstan -c phpstan.neon"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}}