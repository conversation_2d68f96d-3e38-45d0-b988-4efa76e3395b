security:
  password_hashers:
    Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'

  providers:
    slink.user.auth.jwt_user_provider:
      id: Slink\User\Infrastructure\Auth\JwtUserProvider

  firewalls:
    dev:
      pattern: ^/(_(profiler|wdt))/
      security: false

    api:
      pattern: ^/api(?!/(auth|public|guest|image/[^/]+\.[^/]+))
      stateless: true
      jwt:
        provider: slink.user.auth.jwt_user_provider

  access_control:
    # Allow access to all routes, each controller will have
    # its own access control or voter if needed
    - { path: ^/, roles: PUBLIC_ACCESS }

when@test:
  security:
    password_hashers:
      # By default, password hashers are resource intensive and take time. This is
      # important to generate secure password hashes. In tests however, secure hashes
      # are not important, waste resources and increase test times. The following
      # reduces the work factor to the lowest possible values.
      Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
        algorithm: auto
        cost: 4 # Lowest possible value for bcrypt
        time_cost: 3 # Lowest possible value for argon
        memory_cost: 10 # Lowest possible value for argon
