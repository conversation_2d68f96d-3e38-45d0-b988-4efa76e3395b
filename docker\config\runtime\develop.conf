[program:symfony-web-server]
process_name=%(program_name)s
command=symfony server:start --port=8080 --dir=/services/api --no-tls --allow-all-ip
stopwaitsecs=30
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:client-app]
command=yarn --cwd /services/client run dev:with-deps --host
stopwaitsecs=30
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
environment=FORCE_COLOR=true

[program:redis]
command=redis-server --protected-mode no --dir /app/var/data
stopwaitsecs=30
autostart=true
autorestart=true
