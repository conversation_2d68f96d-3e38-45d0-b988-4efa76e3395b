parameters:
  storage:
    provider: '%env(string:STORAGE_PROVIDER)%'
    adapter:
      local:
        dir: '%kernel.project_dir%'
      smb:
        host: '%env(string:default::SMB_HOST)%'
        share: '%env(string:default::SMB_SHARE)%'
        workgroup: '%env(string:default::SMB_WORKGROUP)%'
        username: '%env(string:default::SMB_USERNAME)%'
        password: '%env(string:default::SMB_PASSWORD)%'
      s3:
        region: '%env(string:default::AMAZON_S3_REGION)%'
        bucket: '%env(string:default::AMAZON_S3_BUCKET)%'
        key: '%env(string:default::AMAZON_S3_ACCESS_KEY_ID)%'
        secret: '%env(string:default::AMAZON_S3_SECRET_ACCESS_KEY)%'
  user:
    allowRegistration: true
    approvalRequired: '%env(bool:USER_APPROVAL_REQUIRED)%'
    password:
      minLength: '%env(int:USER_PASSWORD_MIN_LENGTH)%'
      requirements: '%env(int:USER_PASSWORD_REQUIREMENTS)%'
  image:
    maxSize: '%env(string:IMAGE_MAX_SIZE)%'
    stripExifMetadata: '%env(bool:IMAGE_STRIP_EXIF_METADATA)%'
    allowOnlyPublicImages: '%env(bool:IMAGE_ALLOW_ONLY_PUBLIC_IMAGES)%'
    compressionQuality: '%env(int:IMAGE_COMPRESSION_QUALITY)%'
  access:
    allowGuestUploads: '%env(bool:IMAGE_ALLOW_GUEST_UPLOADS)%'
    allowUnauthenticatedAccess: '%env(bool:USER_ALLOW_UNAUTHENTICATED_ACCESS)%'

when@prod:
  parameters:
    storage:
      provider: '%env(string:STORAGE_PROVIDER)%'
      adapter:
        local:
          dir: '/app'
        smb:
          host: '%env(string:default::SMB_HOST)%'
          share: '%env(string:default::SMB_SHARE)%'
          workgroup: '%env(string:default::SMB_WORKGROUP)%'
          username: '%env(string:default::SMB_USERNAME)%'
          password: '%env(string:default::SMB_PASSWORD)%'
        s3:
          region: '%env(string:default::AMAZON_S3_REGION)%'
          bucket: '%env(string:default::AMAZON_S3_BUCKET)%'
          key: '%env(string:default::AMAZON_S3_ACCESS_KEY_ID)%'
          secret: '%env(string:default::AMAZON_S3_SECRET_ACCESS_KEY)%'
