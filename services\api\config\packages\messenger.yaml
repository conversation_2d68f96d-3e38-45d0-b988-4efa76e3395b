framework:
    messenger:
        default_bus: messenger.bus.command
        failure_transport: failed
        buses:
            messenger.bus.command:
                default_middleware: false
                middleware:
                    - handle_message
                    - doctrine_transaction

            messenger.bus.query:
                default_middleware: false
                middleware:
                    - handle_message

            messenger.bus.event:
                default_middleware: allow_no_handlers

        transports:
             # ToDo: add async transport support
             # async: '%env(MESSENGER_TRANSPORT_DSN)%'
             failed: 'doctrine://event_store?queue_name=failed'
             sync: 'sync://'

        routing: ~
            # Slink\Shared\Application\Command\CommandInterface: async

when@test:
    framework:
        messenger:
            transports:
                # replace with your transport name here (e.g., my_transport: 'in-memory://')
                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
                async: 'in-memory://'
