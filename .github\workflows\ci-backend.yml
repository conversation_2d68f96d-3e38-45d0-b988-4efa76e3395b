name: "[backend] CI"

on:
  push:
    branches: [main]
    paths:
      - 'services/api/**'

  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

env:
  image: docker.io/${{ secrets.DOCKERHUB_USERNAME }}/${{ vars.APP_NAME }}
  test_tag: ${{ github.sha }}-test

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: PHPStan Static Analysis
        uses: ./.github/actions/phpstan
        with:
          image: ${{ env.image }}:${{ env.test_tag }}

  test:
    name: Test
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: PHPUnit tests
        uses: ./.github/actions/phpunit
        with:
          image: ${{ env.image }}:${{ env.test_tag }}