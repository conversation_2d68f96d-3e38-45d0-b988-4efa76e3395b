[supervisord]
nodaemon=true
logfile=/dev/null
logfile_maxbytes=0
pidfile=/tmp/supervisord.pid

[program:symfony_about]
command=sh -c "slink slink:about && sleep infinity"
directory=/app
autostart=true
autorestart=false
startretries=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0

[program:startup]
command=/usr/local/bin/startup.sh
autorestart=false
startretries=3
startsecs=0
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0

[include]
files = /etc/supervisor/conf.d/*.conf