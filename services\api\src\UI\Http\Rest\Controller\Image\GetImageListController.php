<?php

declare(strict_types=1);

namespace UI\Http\Rest\Controller\Image;

use Slink\Image\Application\Query\GetImageList\GetImageListQuery;
use Slink\Settings\Application\Service\SettingsService;
use Slink\Settings\Domain\Provider\ConfigurationProviderInterface;
use Slink\Shared\Application\Query\QueryTrait;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\Routing\Attribute\Route;
use UI\Http\Rest\Response\ApiResponse;

#[AsController]
#[Route('/images/{page}', name: 'get_image_list', requirements: ['page' => '\d+'], methods: ['GET'])]
final class GetImageListController {
  use QueryTrait;
  
  /**
   * @param ConfigurationProviderInterface<SettingsService> $configurationProvider
   */
  public function __construct(
    private readonly ConfigurationProviderInterface $configurationProvider
  ) {
  }
  
  public function __invoke(
    #[MapQueryString] GetImageListQuery $query,
    int $page = 1
  ): ApiResponse {
    $isPublicFilter = $this->configurationProvider->get('image.allowOnlyPublicImages') ? null : true;
    
    $images = $this->ask($query->withContext([
      'page' => $page,
      'isPublic' => $isPublicFilter
    ]));
    
    return ApiResponse::collection($images);
  }
}