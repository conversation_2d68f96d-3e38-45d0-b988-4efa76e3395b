name: 'Test'
description: 'Run PHPUnit tests'
inputs:
  image:
    description: 'The name of the Docker image'
    required: true

runs:
  using: 'composite'
  steps:
    - uses: ./.github/actions/build-test-image
      with:
        image: ${{ inputs.image }}

    - shell: bash
      run: |
        docker run --rm ${{ inputs.image }} \
          php /services/api/vendor/bin/phpunit --configuration /services/api/phpunit.xml
