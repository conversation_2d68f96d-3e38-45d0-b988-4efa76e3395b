parameters:
    level: 8
    paths:
        - src/
        - tests/
    ignoreErrors:
        -
            identifier: missingType.generics
        -
            messages:
                - '#^Class [a-zA-Z0-9\\_]+ has an uninitialized readonly property [a-zA-Z0-9\$_]+\. Assign it in the constructor\.$#'
                - '#^Readonly property [a-zA-Z0-9\\_]+\:\:[a-zA-Z0-9\$_]+ is assigned outside of the constructor\.$#'
            paths:
                - src/UI/Http/Rest/Controller/*
                - src/UI/Console/Command/*
                - src/Slink/*/ValueObject/*
        -
            message: '#^Return type of call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\) contains unresolvable type\.$#'
            paths:
                - tests/*