###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
###< symfony/messenger ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
DATABASE_URL="sqlite:////app/var/data/slink.db"
ES_DATABASE_URL="sqlite:////app/var/data/slink_store.db"
###< doctrine/doctrine-bundle ###

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
###< nelmio/cors-bundle ###

###> App Settings ###
STORAGE_PROVIDER=local
USER_ALLOW_UNAUTHENTICATED_ACCESS=true
USER_APPROVAL_REQUIRED=true
USER_PASSWORD_MIN_LENGTH=6
USER_PASSWORD_REQUIREMENTS=15
IMAGE_MAX_SIZE=15M
IMAGE_STRIP_EXIF_METADATA=true
IMAGE_ALLOW_ONLY_PUBLIC_IMAGES=false
IMAGE_ALLOW_GUEST_UPLOADS=false
IMAGE_COMPRESSION_QUALITY=80
###< App Settings ###

###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=super_secret_passphrase
###< lexik/jwt-authentication-bundle ###