imports:
    - { resource: '../doctrine/mappings.yaml' }

doctrine:
    dbal:
        connections:
            event_store:
                url: '%env(resolve:ES_DATABASE_URL)%'
                profiling_collect_backtrace: '%kernel.debug%'
                use_savepoints: true
            read_model:
                url: '%env(resolve:DATABASE_URL)%'
                profiling_collect_backtrace: '%kernel.debug%'
                use_savepoints: true
        default_connection: read_model

        types:
            datetime_immutable: Slink\Shared\Infrastructure\Persistence\Doctrine\Types\DateTimeType
            email: Slink\Shared\Infrastructure\Persistence\Doctrine\Types\EmailType
            username: Slink\Shared\Infrastructure\Persistence\Doctrine\Types\UsernameType
            display_name: Slink\Shared\Infrastructure\Persistence\Doctrine\Types\DisplayNameType
            hashed_password: Slink\Shared\Infrastructure\Persistence\Doctrine\Types\HashedPasswordType

        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '15'
    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        default_entity_manager: read_model
        controller_resolver:
            auto_mapping: false
        entity_managers:
            event_store:
                connection: event_store
            read_model:
                connection: read_model
                report_fields_where_declared: true
                validate_xml_mapping: true
                naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
                mappings: '%doctrine_mappings%'

when@test:
    doctrine:
        dbal:
            # "TEST_TOKEN" is typically set by ParaTest
            dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system
