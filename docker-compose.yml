services:
  slink:
    image: anirdev/slink:latest
    container_name: slink
    environment:
      # Your timezone
      - TZ=UTC

      # Your application hostname (Required for cookies)
      - ORIGIN=http://localhost:3001

      # Require user approval before they can upload images
      - USER_APPROVAL_REQUIRED=true

      # User password requirements
      - USER_PASSWORD_MIN_LENGTH=8
      - USER_PASSWORD_REQUIREMENTS=15 # bitmask of requirements

      # Maximum image size allowed to be uploaded (no more than 50M)
      - IMAGE_MAX_SIZE=15M

      # Image processing settings
      - IMAGE_STRIP_EXIF_METADATA=true
      - IMAGE_COMPRESSION_QUALITY=80

      # Storage provider to use (may require additional configuration variables for different providers, see below)
      - STORAGE_PROVIDER=local
    volumes:
      # Persist the database
      - ./slink/var/data:/app/var/data
      # Persist the uploaded images
      - ./slink/images:/app/slink/images
    ports:
      # Expose the application on port 3000
      - '3001:3000'
