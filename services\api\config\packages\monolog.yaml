monolog:
    channels:
        - deprecation

when@dev:
    monolog:
        handlers:
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine", "!console", "!debug", "!info"]

when@prod:
    monolog:
        handlers:
            error:
                type: fingers_crossed
                action_level: error
                handler: error_log
                excluded_http_codes: [404, 405]
                channels: ["!event"]

            error_log:
                type: stream
                path: php://stderr
                level: error
                formatter: monolog.formatter.line
