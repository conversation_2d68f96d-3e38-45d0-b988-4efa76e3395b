name: 'Prettier'
description: '<PERSON> Prettier on the codebase'

runs:
  using: 'composite'
  steps:
    - uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'yarn'
        cache-dependency-path: './services/client/yarn.lock'

    - run: yarn --cwd ./services/client install --frozen-lockfile
      shell: bash

    - run: yarn --cwd ./services/client run lint
      shell: bash
      continue-on-error: true

    - run: yarn --cwd ./services/client run format
      shell: bash

    - uses: ./.github/actions/commit-bot
      with:
        commit-message: 'chore(prettier): Format codebase'
        push: true
