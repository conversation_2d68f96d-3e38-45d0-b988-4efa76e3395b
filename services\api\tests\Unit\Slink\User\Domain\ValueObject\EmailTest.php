<?php

declare(strict_types=1);

namespace Unit\Slink\User\Domain\ValueObject;

use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\TestCase;
use Slink\User\Domain\Exception\InvalidEmailException;
use Slink\User\Domain\ValueObject\Email;

final class EmailTest extends TestCase {

  /**
   * @return array<string, array{string, string}>
   */
  public static function provideInvalidEmails(): array {
    return [
      'Empty string' => ['', 'Invalid email address.'],
      'No at symbol' => ['invalid', 'Invalid email address.'],
      'Only at symbol' => ['@domain.com', 'Invalid email address.'],
      'Missing domain' => ['user@', 'Invalid email address.'],
      'Double at symbols' => ['user@@domain.com', 'Invalid email address.'],
      'Missing TLD' => ['user@domain', 'Invalid email address.'],
      'Space in email' => ['user <EMAIL>', 'Invalid email address.'],
      'Starting with dot' => ['.<EMAIL>', 'Invalid email address.'],
      'Ending with dot' => ['<EMAIL>', 'Invalid email address.'],
      'Domain starting with dot' => ['<EMAIL>', 'Invalid email address.'],
      'Double dots in domain' => ['<EMAIL>', 'Invalid email address.'],
      'Too long local part' => [str_repeat('a', 65) . '@example.com', 'Invalid email address.'],
      'Invalid characters' => ['user@dom$ain.com', 'Invalid email address.'],
    ];
  }

  /**
   * @return array<string, array{string}>
   */
  public static function provideValidEmails(): array {
    return [
      'Standard email' => ['<EMAIL>'],
      'Email with dot in name' => ['<EMAIL>'],
      'Email with plus tag' => ['<EMAIL>'],
      'Email with numbers' => ['<EMAIL>'],
      'Email with underscore' => ['<EMAIL>'],
      'Short email' => ['<EMAIL>'],
      'Complex email' => ['<EMAIL>'],
      'Email with hyphen' => ['<EMAIL>'],
      'Email with multiple dots' => ['<EMAIL>'],
    ];
  }

  #[Test]
  public function itCreatesValidEmail(): void {
    $emailString = '<EMAIL>';
    $email = Email::fromString($emailString);

    $this->assertInstanceOf(Email::class, $email);
    $this->assertSame($emailString, $email->toString());
  }

  #[Test]
  #[DataProvider('provideValidEmails')]
  public function itCreatesValidEmailsFromDifferentFormats(string $emailString): void {
    $email = Email::fromString($emailString);

    $this->assertInstanceOf(Email::class, $email);
    $this->assertSame($emailString, $email->toString());
  }

  #[Test]
  public function itHandlesCaseSensitivity(): void {
    $email1 = Email::fromString('<EMAIL>');
    $email2 = Email::fromString('<EMAIL>');

    $this->assertNotSame($email1->toString(), $email2->toString());
    $this->assertSame('<EMAIL>', $email1->toString());
  }

  #[Test]
  public function itHandlesEmailEquality(): void {
    $email1 = Email::fromString('<EMAIL>');
    $email2 = Email::fromString('<EMAIL>');
    $email3 = Email::fromString('<EMAIL>');

    $this->assertSame($email1->toString(), $email2->toString());
    $this->assertNotSame($email1->toString(), $email3->toString());
  }

  #[Test]
  public function itReturnsNullForInvalidEmailFromStringOrNull(): void {
    $email = Email::fromStringOrNull('invalid-email');

    $this->assertNull($email);
  }

  #[Test]
  public function itReturnsNullForNullEmail(): void {
    $email = Email::fromStringOrNull(null);

    $this->assertNull($email);
  }

  #[Test]
  public function itReturnsValidEmailFromStringOrNull(): void {
    $emailString = '<EMAIL>';
    $email = Email::fromStringOrNull($emailString);

    $this->assertInstanceOf(Email::class, $email);
    $this->assertSame($emailString, $email->toString());
  }

  #[Test]
  #[DataProvider('provideInvalidEmails')]
  public function itThrowsExceptionForInvalidEmail(string $invalidEmail, string $expectedMessage): void {
    $this->expectException(InvalidEmailException::class);
    $this->expectExceptionMessage($expectedMessage);

    Email::fromString($invalidEmail);
  }
}
