name: "[frontend] CI"

permissions:
  contents: write

on:
  push:
    branches: [main]
    paths:
      - 'services/client/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

jobs:
  format:
    name: Format Codebase
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Prettier Format Check
        uses: ./.github/actions/prettier
